/**
 * 图片处理工具函数
 */

/**
 * 将文件转换为Base64编码
 * @param {File} file - 文件对象
 * @returns {Promise<string>} Base64编码字符串
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error('文件不能为空'));
      return;
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      reject(new Error('只支持图片文件'));
      return;
    }

    const reader = new FileReader();

    reader.onload = (e) => {
      resolve(e.target.result);
    };

    reader.onerror = (e) => {
      reject(new Error('文件读取失败'));
    };

    reader.readAsDataURL(file);
  });
}

/**
 * 获取图片尺寸信息
 * @param {string} src - 图片地址或Base64
 * @returns {Promise<{width: number, height: number}>}
 */
export function getImageDimensions(src) {
  return new Promise((resolve, reject) => {
    const img = new Image();

    // 设置跨域属性，允许加载跨域图片
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
    };

    img.onerror = (error) => {
      console.error('图片加载失败:', error);
      // 如果是跨域问题，尝试不设置crossOrigin再次加载
      if (img.crossOrigin) {
        const img2 = new Image();
        img2.onload = () => {
          resolve({
            width: img2.width,
            height: img2.height
          });
        };
        img2.onerror = () => {
          reject(new Error('图片加载失败'));
        };
        img2.src = src;
      } else {
        reject(new Error('图片加载失败'));
      }
    };

    img.src = src;
  });
}

/**
 * 压缩图片
 * @param {File} file - 原始文件
 * @param {Object} options - 压缩选项
 * @param {number} options.maxWidth - 最大宽度
 * @param {number} options.maxHeight - 最大高度
 * @param {number} options.quality - 压缩质量 (0-1)
 * @returns {Promise<string>} 压缩后的Base64
 */
export function compressImage(file, options = {}) {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // 设置画布尺寸
      canvas.width = width;
      canvas.height = height;

      // 绘制图片
      ctx.drawImage(img, 0, 0, width, height);

      // 转换为Base64
      const compressedBase64 = canvas.toDataURL(file.type, quality);
      resolve(compressedBase64);
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    // 读取文件
    const reader = new FileReader();
    reader.onload = (e) => {
      img.src = e.target.result;
    };
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    reader.readAsDataURL(file);
  });
}

/**
 * 验证图片文件
 * @param {File} file - 文件对象
 * @param {Object} options - 验证选项
 * @param {number} options.maxSize - 最大文件大小(MB)
 * @param {Array<string>} options.allowedTypes - 允许的文件类型
 * @returns {Object} 验证结果
 */
export function validateImageFile(file, options = {}) {
  const {
    maxSize = 5, // 默认5MB
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  } = options;

  const result = {
    valid: true,
    errors: []
  };

  // 检查文件是否存在
  if (!file) {
    result.valid = false;
    result.errors.push('请选择文件');
    return result;
  }

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    result.valid = false;
    result.errors.push(`不支持的文件类型，支持格式：${allowedTypes.map(type => type.split('/')[1]).join(', ')}`);
  }

  // 检查文件大小
  const fileSizeMB = file.size / 1024 / 1024;
  if (fileSizeMB > maxSize) {
    result.valid = false;
    result.errors.push(`文件大小不能超过 ${maxSize}MB，当前文件大小：${fileSizeMB.toFixed(2)}MB`);
  }

  return result;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查URL是否为有效的图片地址
 * @param {string} url - 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export function isValidImageUrl(url) {
  return new Promise((resolve) => {
    if (!url || typeof url !== 'string') {
      resolve(false);
      return;
    }

    // 扩展URL格式检查，支持相对路径和API路径
    const urlPattern = /^(https?:\/\/|\/api\/|\/|\.\/|\.\.\/)/;
    const isValidFormat = urlPattern.test(url) || url.startsWith('data:image');

    if (!isValidFormat) {
      resolve(false);
      return;
    }

    // 如果是Base64格式，直接返回true
    if (url.startsWith('data:image')) {
      resolve(true);
      return;
    }

    // 尝试加载图片
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => resolve(true);
    img.onerror = () => {
      // 如果跨域失败，尝试不设置crossOrigin
      const img2 = new Image();
      img2.onload = () => resolve(true);
      img2.onerror = () => resolve(false);
      img2.src = url;
    };

    img.src = url;
  });
}

/**
 * 从Base64字符串中提取文件信息
 * @param {string} base64 - Base64字符串
 * @returns {Object} 文件信息
 */
export function parseBase64Info(base64) {
  if (!base64 || !base64.startsWith('data:')) {
    return null;
  }

  try {
    const [header, data] = base64.split(',');
    const mimeMatch = header.match(/data:([^;]+)/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'unknown';

    // 计算文件大小（Base64编码后的大小约为原文件的4/3）
    const sizeInBytes = (data.length * 3) / 4;

    return {
      mimeType,
      size: sizeInBytes,
      formattedSize: formatFileSize(sizeInBytes),
      isImage: mimeType.startsWith('image/')
    };
  } catch (error) {
    console.error('解析Base64信息失败:', error);
    return null;
  }
}

/**
 * 创建图片预览缩略图
 * @param {string} src - 图片源
 * @param {Object} options - 选项
 * @param {number} options.width - 缩略图宽度
 * @param {number} options.height - 缩略图高度
 * @returns {Promise<string>} 缩略图Base64
 */
export function createThumbnail(src, options = {}) {
  const { width = 150, height = 150 } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = width;
      canvas.height = height;

      // 计算居中裁剪的位置
      const scale = Math.max(width / img.width, height / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (width - scaledWidth) / 2;
      const y = (height - scaledHeight) / 2;

      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

      const thumbnail = canvas.toDataURL('image/jpeg', 0.8);
      resolve(thumbnail);
    };

    img.onerror = () => {
      reject(new Error('创建缩略图失败'));
    };

    img.src = src;
  });
}
