[2m2025-08-07 00:05:37.182[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-07 00:05:37.293[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-07 00:05:37.293[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-07 00:05:37.334[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-07 00:05:37.337[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-07 00:05:37.385[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 00:05:37.423[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-07 00:05:37.436[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-07 00:05:37.440[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-07 00:05:37.442[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-07 00:05:37.444[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-08-07 00:05:37.454[0;39m [32mDEBUG[0;39m [35m5951[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
